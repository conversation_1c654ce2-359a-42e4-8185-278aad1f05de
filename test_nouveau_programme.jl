#!/usr/bin/env julia

# Test du nouveau programme d'analyse des formules
include("nouveau_analyseur_formules.jl")

println("🧪 TEST DU NOUVEAU PROGRAMME")
println("=" ^ 50)

# Test 1: Création de l'analyseur
println("\n1️⃣ Test de création de l'analyseur...")
try
    analyzer = FormuleAnalyzer{Float64}()
    println("✅ Analyseur créé avec succès!")
    println("   - Base logarithmique: $(analyzer.base)")
    println("   - Epsilon: $(analyzer.epsilon)")
    println("   - Nombre de probabilités théoriques: $(length(analyzer.theoretical_probs))")
catch e
    println("❌ Erreur lors de la création: $e")
end

# Test 2: Vérification des probabilités théoriques
println("\n2️⃣ Test des probabilités théoriques...")
try
    analyzer = FormuleAnalyzer{Float64}()
    total_prob = sum(values(analyzer.theoretical_probs))
    println("✅ Probabilités théoriques chargées!")
    println("   - Somme totale: $total_prob")
    println("   - Normalisées: $(abs(total_prob - 1.0) < 1e-10 ? "✅" : "❌")")
    
    # Afficher quelques probabilités
    println("   - Exemples:")
    count = 0
    for (key, prob) in analyzer.theoretical_probs
        if count < 3
            println("     * $key: $prob")
            count += 1
        end
    end
catch e
    println("❌ Erreur: $e")
end

# Test 3: Test d'une formule simple
println("\n3️⃣ Test d'une formule simple...")
try
    analyzer = FormuleAnalyzer{Float64}()
    test_sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", "1_A_BANKER"]
    
    # Test Formule 1A
    result_1A = calculer_formule1A_shannon_jointe_obs(analyzer, test_sequence)
    println("✅ Formule 1A calculée!")
    println("   - Séquence test: $test_sequence")
    println("   - Résultat F1A (Shannon Obs): $result_1A bits")
    
    # Test Formule 1B
    result_1B = calculer_formule1B_shannon_jointe_theo(analyzer, test_sequence)
    println("✅ Formule 1B calculée!")
    println("   - Résultat F1B (Shannon Theo): $result_1B bits")
    
catch e
    println("❌ Erreur: $e")
end

# Test 4: Test du calcul complet
println("\n4️⃣ Test du calcul complet...")
try
    analyzer = FormuleAnalyzer{Float64}()
    test_sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    
    println("   - Calcul des 24 formules en cours...")
    results = calculate_all_formules_evolution(analyzer, test_sequence)
    
    println("✅ Calcul complet réussi!")
    println("   - Nombre de positions: $(length(results))")
    println("   - Dernière position: $(results[end].position)")
    
    # Afficher quelques résultats finaux
    final = results[end]
    println("   - F1A final: $(final.formule1A_shannon_jointe_obs)")
    println("   - F1B final: $(final.formule1B_shannon_jointe_theo)")
    println("   - F2A final: $(final.formule2A_aep_obs)")
    
catch e
    println("❌ Erreur: $e")
end

println("\n🎉 TESTS TERMINÉS!")
println("=" ^ 50)
