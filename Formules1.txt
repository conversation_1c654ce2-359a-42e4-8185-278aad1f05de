═══════════════════════════════════════════════════════════════════════════════
FORMULES POUR FENÊTRES CROISSANTES - DOUBLE IMPLÉMENTATION
═══════════════════════════════════════════════════════════════════════════════

PRINCIPE GÉNÉRAL :
Chaque formule est implémentée DEUX FOIS :
- VERSION OBSERVÉE : Utilise les fréquences observées dans la séquence
- VERSION THÉORIQUE : Utilise les probabilités théoriques INDEX5

NOTATION :
- p_obs(x) = fréquence observée de x dans la séquence / longueur séquence
- p_theo(x) = probabilité théorique INDEX5 de x (entropie_baccarat_analyzer.jl)

═══════════════════════════════════════════════════════════════════════════════

FORMULES FONDAMENTALES POUR FENÊTRES CROISSANTES

1. ENTROPIE DE SHANNON JOINTE

1A. VERSION OBSERVÉE
Formule : H_obs(X₁, X₂, ..., Xₙ) = -∑ p_obs(x₁,...,xₙ) log₂ p_obs(x₁,...,xₙ)

Ce qu'elle calcule :
- Quantité d'information réellement observée dans la séquence
- Incertitude basée sur les patterns effectivement vus
- Application baccarat : Entropie empirique de la séquence jouée

1B. VERSION THÉORIQUE
Formule : H_theo(X₁, X₂, ..., Xₙ) = -∑ p_theo(x₁,...,xₙ) log₂ p_theo(x₁,...,xₙ)

Ce qu'elle calcule :
- Quantité d'information théorique selon le modèle INDEX5
- Incertitude attendue selon les probabilités de référence
- Application baccarat : Entropie théorique selon le modèle INDEX5

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 912, 1160, 1723-1725)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE :
1. Compter les occurrences de chaque combinaison (x₁,...,xₙ) dans la séquence
2. Calculer p_obs(x₁,...,xₙ) = count(x₁,...,xₙ) / nombre_total_combinaisons
3. Appliquer la formule : H_obs = -∑ p_obs(x₁,...,xₙ) × log₂(p_obs(x₁,...,xₙ))

VERSION THÉORIQUE :
1. Pour chaque combinaison possible (x₁,...,xₙ)
2. Calculer p_theo(x₁,...,xₙ) selon les probabilités INDEX5
3. Appliquer la formule : H_theo = -∑ p_theo(x₁,...,xₙ) × log₂(p_theo(x₁,...,xₙ))

2. ENTROPIE PAR SYMBOLE (AEP - Asymptotic Equipartition Property)

2A. VERSION OBSERVÉE
Formule : H_AEP_obs(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_obs(xᵢ)

Ce qu'elle calcule :
- Entropie moyenne par symbole basée sur les fréquences observées
- Information moyenne par main selon les patterns réels
- Application baccarat : Entropie empirique moyenne par main INDEX5

2B. VERSION THÉORIQUE
Formule : H_AEP_theo(X₁ⁿ) = -(1/n) ∑ᵢ₌₁ⁿ log₂ p_theo(xᵢ)

Ce qu'elle calcule :
- Entropie moyenne par symbole selon le modèle INDEX5
- Information théorique moyenne par main
- Application baccarat : Entropie théorique moyenne par main INDEX5

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 1151-1154)
- Théorème 12 (Shannon, McMillan, Breiman)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE :
1. Pour chaque symbole xᵢ dans la séquence X₁ⁿ = [x₁, x₂, ..., xₙ]
2. Calculer p_obs(xᵢ) = count(xᵢ in sequence) / n
3. Calculer log₂(p_obs(xᵢ)) avec protection contre log(0)
4. Sommer : ∑ᵢ₌₁ⁿ log₂(p_obs(xᵢ))
5. Appliquer : H_AEP_obs = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_obs(xᵢ))

VERSION THÉORIQUE :
1. Pour chaque symbole xᵢ dans la séquence X₁ⁿ = [x₁, x₂, ..., xₙ]
2. Récupérer p_theo(xᵢ) depuis les probabilités INDEX5
3. Calculer log₂(p_theo(xᵢ)) avec protection contre log(0)
4. Sommer : ∑ᵢ₌₁ⁿ log₂(p_theo(xᵢ))
5. Appliquer : H_AEP_theo = -(1/n) × ∑ᵢ₌₁ⁿ log₂(p_theo(xᵢ))

3. TAUX D'ENTROPIE (ENTROPY RATE)

3A. VERSION OBSERVÉE
Formule : H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁, X₂, ..., Xₙ)

Ce qu'elle calcule :
- Limite asymptotique de l'entropie observée par symbole
- Complexité intrinsèque mesurée empiriquement
- Application baccarat : Complexité réelle du système observé

3B. VERSION THÉORIQUE
Formule : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, X₂, ..., Xₙ)

Ce qu'elle calcule :
- Limite asymptotique de l'entropie théorique par symbole
- Complexité intrinsèque selon le modèle INDEX5
- Application baccarat : Complexité théorique du système INDEX5

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 916-919 et 923-925)
- Définition 60 et Théorème 10

MÉTHODE DE CALCUL :
VERSION OBSERVÉE :
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer H_obs(X₁, ..., Xₙ) (entropie jointe observée, voir Formule 1A)
3. Calculer (1/n) × H_obs(X₁, ..., Xₙ)
4. Estimer la limite : H_obs(Ξ) = lim_{n→∞} (1/n) H_obs(X₁, ..., Xₙ)

VERSION THÉORIQUE :
1. Pour chaque longueur n = 1, 2, 3, ..., N
2. Calculer H_theo(X₁, ..., Xₙ) (entropie jointe théorique, voir Formule 1B)
3. Calculer (1/n) × H_theo(X₁, ..., Xₙ)
4. Estimer la limite : H_theo(Ξ) = lim_{n→∞} (1/n) H_theo(X₁, ..., Xₙ)

4. ENTROPIE MÉTRIQUE (KOLMOGOROV-SINAI)

4A. VERSION OBSERVÉE
Formule : h_μ_obs(T) = sup{h_μ_obs(T, α) : α partition finie}
où h_μ_obs(T, α) = lim_{n→∞} (1/n) H_μ_obs(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

Ce qu'elle calcule :
- Entropie dynamique observée du système de transformation T
- Taux de création d'information mesuré empiriquement
- Supremum sur toutes les partitions possibles avec mesure observée
- Application baccarat : Complexité dynamique empirique du processus INDEX5

4B. VERSION THÉORIQUE
Formule : h_μ_theo(T) = sup{h_μ_theo(T, α) : α partition finie}
où h_μ_theo(T, α) = lim_{n→∞} (1/n) H_μ_theo(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

Ce qu'elle calcule :
- Entropie dynamique théorique du système selon le modèle INDEX5
- Taux de création d'information selon les probabilités de référence
- Supremum sur toutes les partitions possibles avec mesure théorique
- Application baccarat : Complexité dynamique théorique du modèle INDEX5

SOURCE EXACTE : cours_entropie/niveau_expert/01_entropie_metrique.md (lignes 25, 30)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE :
1. Construire la mesure μ_obs à partir des fréquences observées dans la séquence
2. Choisir une partition α = {A₁, A₂, ..., Aₖ} de l'espace des états INDEX5
3. Pour chaque n, calculer la partition jointe : ⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα
4. Calculer H_μ_obs(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα) = -∑ μ_obs(B) log₂ μ_obs(B)
5. Estimer h_μ_obs(T, α) = lim_{n→∞} (1/n) H_μ_obs(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
6. Prendre le supremum : h_μ_obs(T) = sup_α h_μ_obs(T, α)

VERSION THÉORIQUE :
1. Construire la mesure μ_theo à partir des probabilités INDEX5
2. Choisir une partition α = {A₁, A₂, ..., Aₖ} de l'espace des états INDEX5
3. Pour chaque n, calculer la partition jointe : ⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα
4. Calculer H_μ_theo(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα) = -∑ μ_theo(B) log₂ μ_theo(B)
5. Estimer h_μ_theo(T, α) = lim_{n→∞} (1/n) H_μ_theo(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
6. Prendre le supremum : h_μ_theo(T) = sup_α h_μ_theo(T, α)

APPROXIMATION PRATIQUE (selon lignes 232-233) :
1. Choisir une partition fine α
2. Calculer H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα) pour n croissant
3. Estimer la limite (1/n)H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)

5. ENTROPIE CONDITIONNELLE CUMULATIVE

5A. VERSION OBSERVÉE
Formule : H_obs(Xₙ|X₁, ..., Xₙ₋₁) = H_obs(X₁, ..., Xₙ) - H_obs(X₁, ..., Xₙ₋₁)

Ce qu'elle calcule :
- Information observée apportée par le n-ème symbole
- Prédictibilité empirique basée sur l'historique observé
- Application baccarat : Prédictibilité réelle de la main n

5B. VERSION THÉORIQUE
Formule : H_theo(Xₙ|X₁, ..., Xₙ₋₁) = H_theo(X₁, ..., Xₙ) - H_theo(X₁, ..., Xₙ₋₁)

Ce qu'elle calcule :
- Information théorique apportée par le n-ème symbole
- Prédictibilité selon le modèle INDEX5
- Application baccarat : Prédictibilité théorique de la main n

SOURCE EXACTE : cours_entropie/niveau_debutant/03_entropie_conditionnelle.md (lignes 2, 24, 58, 63)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE :
1. Calculer H_obs(X₁, ..., Xₙ) (entropie jointe observée, voir Formule 1A)
2. Calculer H_obs(X₁, ..., Xₙ₋₁) (entropie jointe observée de n-1 variables)
3. Appliquer : H_obs(Xₙ|X₁, ..., Xₙ₋₁) = H_obs(X₁, ..., Xₙ) - H_obs(X₁, ..., Xₙ₋₁)

VERSION THÉORIQUE :
1. Calculer H_theo(X₁, ..., Xₙ) (entropie jointe théorique, voir Formule 1B)
2. Calculer H_theo(X₁, ..., Xₙ₋₁) (entropie jointe théorique de n-1 variables)
3. Appliquer : H_theo(Xₙ|X₁, ..., Xₙ₋₁) = H_theo(X₁, ..., Xₙ) - H_theo(X₁, ..., Xₙ₋₁)
═══════════════════════════════════════════════════════════════════════════════

🔬 FORMULES AVANCÉES POUR FENÊTRES CROISSANTES

6. ENTROPIE RELATIVE (DIVERGENCE KL) CUMULATIVE

6A. VERSION OBSERVÉE vs THÉORIQUE
Formule : D_KL_obs_theo(P_n||Q_n) = ∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂(p_obs(xᵢ)/p_theo(xᵢ))

Ce qu'elle calcule :
- Distance entre distribution observée et théorique INDEX5
- Mesure l'écart des fréquences réelles par rapport au modèle INDEX5
- Application baccarat : Écart entre patterns observés et probabilités INDEX5

6B. VERSION THÉORIQUE vs UNIFORME
Formule : D_KL_theo_unif(P_n||U_n) = ∑ᵢ₌₁ⁿ p_theo(xᵢ) log₂(p_theo(xᵢ)/p_unif(xᵢ))

Ce qu'elle calcule :
- Distance entre modèle INDEX5 et distribution uniforme
- Mesure l'écart du modèle INDEX5 par rapport à l'équiprobabilité
- Application baccarat : Biais intrinsèque du modèle INDEX5

SOURCE EXACTE : cours_entropie/niveau_intermediaire/01_entropie_relative.md (lignes 2, 14, 19-21, 28)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE vs THÉORIQUE :
1. Pour chaque position i = 1, ..., n dans la séquence
2. Calculer p_obs(xᵢ) = count(xᵢ in sequence) / n
3. Récupérer p_theo(xᵢ) depuis les probabilités INDEX5
4. Calculer : p_obs(xᵢ) × log₂(p_obs(xᵢ)/p_theo(xᵢ))
5. Sommer : D_KL_obs_theo = ∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂(p_obs(xᵢ)/p_theo(xᵢ))

VERSION THÉORIQUE vs UNIFORME :
1. Pour chaque valeur INDEX5 possible
2. Récupérer p_theo(x) depuis les probabilités INDEX5
3. Calculer p_unif(x) = 1/18 (distribution uniforme sur 18 valeurs)
4. Calculer : p_theo(x) × log₂(p_theo(x)/p_unif(x))
5. Sommer : D_KL_theo_unif = ∑ p_theo(x) log₂(p_theo(x)/p_unif(x))

7. INFORMATION MUTUELLE CUMULATIVE

7A. VERSION OBSERVÉE
Formule : I_obs(X₁ⁿ; Y₁ⁿ) = H_obs(X₁ⁿ) + H_obs(Y₁ⁿ) - H_obs(X₁ⁿ, Y₁ⁿ)

Ce qu'elle calcule :
- Dépendance observée entre deux séquences croissantes
- Information partagée mesurée empiriquement
- Application baccarat : Corrélation réelle entre patterns observés

7B. VERSION THÉORIQUE
Formule : I_theo(X₁ⁿ; Y₁ⁿ) = H_theo(X₁ⁿ) + H_theo(Y₁ⁿ) - H_theo(X₁ⁿ, Y₁ⁿ)

Ce qu'elle calcule :
- Dépendance théorique selon le modèle INDEX5
- Information partagée attendue selon les probabilités de référence
- Application baccarat : Corrélation théorique entre patterns INDEX5

SOURCE EXACTE : cours_entropie/niveau_debutant/03_entropie_conditionnelle.md (lignes 87, 91, 100)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE :
1. Construire les distributions observées pour les deux séquences
2. Calculer H_obs(X₁ⁿ), H_obs(Y₁ⁿ), H_obs(X₁ⁿ, Y₁ⁿ) selon Formule 1A
3. Appliquer : I_obs(X₁ⁿ; Y₁ⁿ) = H_obs(X₁ⁿ) + H_obs(Y₁ⁿ) - H_obs(X₁ⁿ, Y₁ⁿ)

VERSION THÉORIQUE :
1. Utiliser les probabilités INDEX5 pour les deux séquences
2. Calculer H_theo(X₁ⁿ), H_theo(Y₁ⁿ), H_theo(X₁ⁿ, Y₁ⁿ) selon Formule 1B
3. Appliquer : I_theo(X₁ⁿ; Y₁ⁿ) = H_theo(X₁ⁿ) + H_theo(Y₁ⁿ) - H_theo(X₁ⁿ, Y₁ⁿ)

8. ENTROPIE CROISÉE CUMULATIVE

8A. VERSION OBSERVÉE vs THÉORIQUE
Formule : H_cross_obs_theo(P_n, Q_n) = -∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂ p_theo(xᵢ)

Ce qu'elle calcule :
- Coût d'encodage des données observées avec le modèle INDEX5
- Efficacité du modèle INDEX5 pour encoder les patterns réels
- Application baccarat : Performance prédictive du modèle INDEX5

8B. VERSION THÉORIQUE vs UNIFORME
Formule : H_cross_theo_unif(P_n, U_n) = -∑ᵢ₌₁ⁿ p_theo(xᵢ) log₂ p_unif(xᵢ)

Ce qu'elle calcule :
- Coût d'encodage du modèle INDEX5 avec distribution uniforme
- Inefficacité de supposer l'équiprobabilité pour INDEX5
- Application baccarat : Avantage informationnel du modèle INDEX5

SOURCE EXACTE : cours_entropie/niveau_intermediaire/01_entropie_relative.md (lignes 127, 129, 132, 135)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE vs THÉORIQUE :
1. Pour chaque position i = 1, ..., n dans la séquence
2. Calculer p_obs(xᵢ) = count(xᵢ in sequence) / n
3. Récupérer p_theo(xᵢ) depuis les probabilités INDEX5
4. Calculer : p_obs(xᵢ) × log₂(p_theo(xᵢ))
5. Sommer avec signe négatif : H_cross_obs_theo = -∑ᵢ₌₁ⁿ p_obs(xᵢ) log₂(p_theo(xᵢ))

VERSION THÉORIQUE vs UNIFORME :
1. Pour chaque valeur INDEX5 possible
2. Récupérer p_theo(x) depuis les probabilités INDEX5
3. Calculer p_unif(x) = 1/18 (distribution uniforme)
4. Calculer : p_theo(x) × log₂(p_unif(x))
5. Sommer avec signe négatif : H_cross_theo_unif = -∑ p_theo(x) log₂(p_unif(x))

9. ENTROPIE TOPOLOGIQUE CUMULATIVE

9A. VERSION OBSERVÉE
Formule : h_top_obs(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_obs(ε)

Ce qu'elle calcule :
- Complexité topologique observée du système dynamique
- Croissance des orbites distinctes mesurée empiriquement
- Application baccarat : Complexité géométrique réelle des trajectoires

9B. VERSION THÉORIQUE
Formule : h_top_theo(f) = lim_{ε→0} lim_{n→∞} (1/n) log s_n_theo(ε)

Ce qu'elle calcule :
- Complexité topologique théorique selon le modèle INDEX5
- Croissance attendue des orbites selon les probabilités de référence
- Application baccarat : Complexité géométrique théorique INDEX5

SOURCE EXACTE : cours_entropie/niveau_expert/02_entropie_topologique.md (lignes 2, 25, 42, 44)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE :
1. Utiliser les patterns réellement observés dans la séquence
2. Compter les motifs distincts de longueur croissante
3. Estimer s_n_obs(ε) = nombre de motifs observés distincts
4. Calculer h_top_obs(f) ≈ lim_{n→∞} (1/n) log(nombre_motifs_distincts_n)

VERSION THÉORIQUE :
1. Utiliser les probabilités INDEX5 pour générer les patterns théoriques
2. Calculer s_n_theo(ε) selon la distribution théorique
3. Estimer h_top_theo(f) selon le modèle INDEX5
═══════════════════════════════════════════════════════════════════════════════

📐 FORMULES SPÉCIALISÉES POUR L'ANALYSE TEMPORELLE

10. ENTROPIE DE BLOCK CUMULATIVE

10A. VERSION OBSERVÉE
Formule : H_n_obs = H_obs(X₁, X₂, ..., Xₙ) avec H_{n+1}_obs ≥ H_n_obs

Ce qu'elle calcule :
- Séquence croissante d'entropies observées de blocs
- Évolution réelle de l'information avec la longueur
- Application baccarat : Croissance empirique de l'information

10B. VERSION THÉORIQUE
Formule : H_n_theo = H_theo(X₁, X₂, ..., Xₙ) avec H_{n+1}_theo ≥ H_n_theo

Ce qu'elle calcule :
- Séquence croissante d'entropies théoriques de blocs
- Évolution attendue selon le modèle INDEX5
- Application baccarat : Croissance théorique de l'information INDEX5

SOURCE EXACTE : IDENTIQUE À LA FORMULE 1

MÉTHODE DE CALCUL :
VERSION OBSERVÉE : IDENTIQUE À LA FORMULE 1A
VERSION THÉORIQUE : IDENTIQUE À LA FORMULE 1B

11. ENTROPIE CONDITIONNELLE DÉCROISSANTE

11A. VERSION OBSERVÉE
Formule : u_n_obs = H_obs(Xₙ|X₁, ..., Xₙ₋₁) avec u_{n+1}_obs ≤ u_n_obs

Ce qu'elle calcule :
- Séquence décroissante d'entropies conditionnelles observées
- Amélioration réelle de la prédictibilité avec l'historique
- Application baccarat : Amélioration empirique de la prédiction

11B. VERSION THÉORIQUE
Formule : u_n_theo = H_theo(Xₙ|X₁, ..., Xₙ₋₁) avec u_{n+1}_theo ≤ u_n_theo

Ce qu'elle calcule :
- Séquence décroissante d'entropies conditionnelles théoriques
- Amélioration attendue selon le modèle INDEX5
- Application baccarat : Amélioration théorique de la prédiction INDEX5

SOURCE EXACTE : cours_entropie/resume_D4MA1C20_2012.md (lignes 923-925, 930-932)

MÉTHODE DE CALCUL :
VERSION OBSERVÉE : IDENTIQUE À LA FORMULE 5A
VERSION THÉORIQUE : IDENTIQUE À LA FORMULE 5B

12. THÉORÈME AEP (SHANNON-MCMILLAN-BREIMAN)

12A. VERSION OBSERVÉE
Formule : lim_{n→∞} -(1/n) log₂ p_obs(X₁ⁿ) = H_obs(Ξ) presque sûrement

Ce qu'elle calcule :
- Convergence de l'information observée par symbole
- Propriété d'équipartition asymptotique empirique
- Application baccarat : Convergence vers l'entropie observée du processus

12B. VERSION THÉORIQUE
Formule : lim_{n→∞} -(1/n) log₂ p_theo(X₁ⁿ) = H_theo(Ξ) presque sûrement

Ce qu'elle calcule :
- Convergence de l'information théorique par symbole
- Propriété d'équipartition asymptotique selon INDEX5
- Application baccarat : Convergence vers l'entropie théorique INDEX5

SOURCE EXACTE : IDENTIQUE À LA FORMULE 2

MÉTHODE DE CALCUL :
VERSION OBSERVÉE : IDENTIQUE À LA FORMULE 2A
VERSION THÉORIQUE : IDENTIQUE À LA FORMULE 2B

═══════════════════════════════════════════════════════════════════════════════

🎯 SYNTHÈSE : 24 IMPLÉMENTATIONS REQUISES (12 FORMULES × 2 VERSIONS)
═══════════════════════════════════════════════════════════════════════════════

RÉCAPITULATIF DES 24 FONCTIONS À IMPLÉMENTER :

FORMULES FONDAMENTALES (10 fonctions) :
1A. calculer_formule1A_shannon_jointe_obs()
1B. calculer_formule1B_shannon_jointe_theo()
2A. calculer_formule2A_aep_obs()
2B. calculer_formule2B_aep_theo()
3A. calculer_formule3A_taux_entropie_obs()
3B. calculer_formule3B_taux_entropie_theo()
4A. calculer_formule4A_entropie_metrique_obs()
4B. calculer_formule4B_entropie_metrique_theo()
5A. calculer_formule5A_conditionnelle_obs()
5B. calculer_formule5B_conditionnelle_theo()

FORMULES AVANCÉES (8 fonctions) :
6A. calculer_formule6A_divergence_kl_obs_theo()
6B. calculer_formule6B_divergence_kl_theo_unif()
7A. calculer_formule7A_information_mutuelle_obs()
7B. calculer_formule7B_information_mutuelle_theo()
8A. calculer_formule8A_entropie_croisee_obs_theo()
8B. calculer_formule8B_entropie_croisee_theo_unif()
9A. calculer_formule9A_entropie_topologique_obs()
9B. calculer_formule9B_entropie_topologique_theo()

FORMULES SPÉCIALISÉES (6 fonctions) :
10A. calculer_formule10A_block_cumulative_obs()
10B. calculer_formule10B_block_cumulative_theo()
11A. calculer_formule11A_conditionnelle_decroissante_obs()
11B. calculer_formule11B_conditionnelle_decroissante_theo()
12A. calculer_formule12A_theoreme_aep_obs()
12B. calculer_formule12B_theoreme_aep_theo()

DISTINCTION CLAIRE ENTRE LES DEUX CAS D'USAGE :
✅ VERSION OBSERVÉE (A) : Utilise les fréquences réelles de la séquence
✅ VERSION THÉORIQUE (B) : Utilise les probabilités INDEX5 d'entropie_baccarat_analyzer.jl

AVANTAGES DE CETTE APPROCHE :
🔍 Comparaison directe entre réalité et modèle théorique
📊 Validation de la qualité du modèle INDEX5
⚖️ Mesure des écarts entre observations et prédictions
🎯 Double perspective sur chaque aspect mathématique

LIEN AVEC FORMULE 2 : Cette formule EST la Formule 2 dans sa forme limite asymptotique